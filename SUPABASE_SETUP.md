# Supabase Integration Setup Guide

This guide walks you through setting up Supabase integration for the transaction data pipeline.

## Prerequisites

1. **Supabase Project**: You should have access to the "phia interior dash" project
2. **Python Dependencies**: Install required packages
3. **Environment Variables**: Configure Supabase credentials

## 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Key packages:
- `supabase>=2.0.0` - Python client for Supabase
- `python-dotenv` - Environment variable management

## 2. Get Supabase Credentials

### From Supabase Dashboard:

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select the "phia interior dash" project
3. Navigate to **Settings** → **API**
4. Copy the following values:
   - **Project URL**: `https://fghareehgekdxwljswjg.supabase.co`
   - **anon public key**: For frontend access
   - **service_role secret**: For backend/DAG operations

## 3. Configure Environment Variables

Add these to your `.env` file:

```bash
# Supabase Configuration
SUPABASE_URL=https://fghareehgekdxwljswjg.supabase.co
SUPABASE_SERVICE_KEY=your_service_role_secret_here
SUPABASE_ANON_KEY=your_anon_public_key_here
```

## 4. Set Up Database Schema

### Option A: Manual Setup (Recommended)

1. Go to Supabase Dashboard → SQL Editor
2. Copy the contents of `supabase/schema.sql`
3. Run the SQL to create tables, indexes, and views

### Option B: Programmatic Setup

```python
from dags.dependencies.supabase_client import SupabaseClientLocal

client = SupabaseClientLocal()
success = client.create_table_if_not_exists()
print(f"Schema setup: {'Success' if success else 'Failed'}")
```

## 5. Test the Integration

Run the comprehensive test script:

```bash
python test_supabase_integration.py
```

This will test:
- ✅ Strackr API connection
- ✅ Data transformation
- ✅ Supabase connection
- ✅ Data upload to Supabase
- ✅ Data retrieval from Supabase

## 6. Database Schema Overview

### Main Table: `normalized_transactions`

Stores all transaction data from both Strackr and ShopMy:

```sql
CREATE TABLE normalized_transactions (
    id UUID PRIMARY KEY,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    platform VARCHAR(50) NOT NULL, -- 'strackr' or 'shopmy'
    source_transaction_id VARCHAR(255) NOT NULL,
    
    -- Financial data
    currency CHAR(3) NOT NULL,
    order_amount DECIMAL(15,2) NOT NULL,
    commission_amount DECIMAL(15,2) NOT NULL,
    
    -- Transaction details
    order_id VARCHAR(255) NOT NULL,
    transaction_date TIMESTAMPTZ NOT NULL,
    created_date TIMESTAMPTZ NOT NULL,
    
    -- Merchant/Network info
    network_name VARCHAR(255) NOT NULL,
    merchant_name VARCHAR(255) NOT NULL,
    
    -- Status and metadata
    status VARCHAR(50) NOT NULL,
    custom_fields JSONB,
    
    -- Audit fields
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Views for Analytics

1. **`transaction_summary`**: Daily transaction summaries by platform/status
2. **`daily_platform_summary`**: Platform performance metrics
3. **`merchant_performance`**: Merchant-level analytics

## 7. Frontend Integration

### Query Examples

```javascript
// Get recent transactions
const { data: transactions } = await supabase
  .from('normalized_transactions')
  .select('*')
  .eq('platform', 'strackr')
  .order('transaction_date', { ascending: false })
  .limit(100);

// Get daily summary
const { data: summary } = await supabase
  .from('daily_platform_summary')
  .select('*')
  .gte('date', '2025-06-01')
  .order('date', { ascending: false });

// Get merchant performance
const { data: merchants } = await supabase
  .from('merchant_performance')
  .select('*')
  .eq('platform', 'strackr')
  .order('total_commission', { ascending: false });
```

## 8. DAG Integration

The Strackr DAG now includes a Supabase upload task:

```
fetch_transactions → transform_data → validate_and_store → upload_to_supabase
```

### Task Flow:
1. **Fetch**: Get transactions from Strackr API
2. **Transform**: Convert to normalized schema
3. **Validate & Store**: Quality checks + GCS storage
4. **Upload to Supabase**: Store in database for frontend access

## 9. Monitoring and Troubleshooting

### Common Issues:

1. **Connection Errors**:
   - Check `SUPABASE_URL` and `SUPABASE_SERVICE_KEY`
   - Verify network connectivity

2. **Permission Errors**:
   - Ensure service key has proper permissions
   - Check Row Level Security (RLS) policies

3. **Data Validation Errors**:
   - Check transaction schema compliance
   - Review validation error logs

### Monitoring Queries:

```sql
-- Check recent uploads
SELECT platform, DATE(created_at) as date, COUNT(*) as count
FROM normalized_transactions
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY platform, DATE(created_at)
ORDER BY date DESC;

-- Check for duplicates
SELECT transaction_id, COUNT(*) as count
FROM normalized_transactions
GROUP BY transaction_id
HAVING COUNT(*) > 1;
```

## 10. Security Considerations

1. **Row Level Security**: Enabled on all tables
2. **Service Role**: Used for DAG operations only
3. **Anon Key**: For frontend read-only access
4. **Environment Variables**: Never commit credentials to git

## Next Steps

1. Set up the schema in Supabase
2. Configure environment variables
3. Run the test script to validate setup
4. Deploy the updated DAG
5. Monitor the first few runs
6. Set up frontend queries for your dashboard

For questions or issues, check the Airflow logs or run the test script for diagnostics.
