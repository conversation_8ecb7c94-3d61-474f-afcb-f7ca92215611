# Core dependencies for the data pipeline

# Airflow (for DAG execution)
apache-airflow>=2.5.0

# API clients and HTTP requests
requests>=2.28.0
python-dotenv>=0.19.0

# Data processing and validation
pandas>=1.5.0
pydantic>=1.10.0

# Database and storage
supabase>=2.0.0
google-cloud-storage>=2.7.0
google-cloud-secret-manager>=2.15.0

# Date and time handling
python-dateutil>=2.8.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.20.0

# Optional: For enhanced logging and monitoring
structlog>=22.0.0
