#!/usr/bin/env python3
"""
Test script to push all available Strackr data to Supabase.
This script fetches all recent transactions and uploads them to demonstrate the full pipeline.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import required modules
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our modules with proper path handling
sys.path.append("dags/dependencies/transaction_reports")
sys.path.append("dags/dependencies")

# Import modules individually to handle import errors better
try:
    # Import transformation modules
    import strackr_transform
    import schema

    print("✅ Transformation modules imported successfully")
except ImportError as e:
    print(f"❌ Transformation import error: {e}")
    sys.exit(1)

try:
    # Import Supabase modules
    from supabase_client import SupabaseClientLocal

    print("✅ Supabase client imported successfully")
except ImportError as e:
    print(f"❌ Supabase client import error: {e}")
    print("Make sure supabase is installed: pip install supabase")
    sys.exit(1)


def fetch_all_strackr_transactions(days_back: int = 30, max_transactions: int = 1000) -> List[Dict[str, Any]]:
    """Fetch all recent transactions from Strackr API with pagination."""
    api_id = os.getenv("STRACKR_API_ID")
    api_key = os.getenv("STRACKR_API_KEY")

    if not api_id or not api_key:
        raise ValueError(
            "Missing STRACKR_API_ID or STRACKR_API_KEY environment variables"
        )

    # Get date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)

    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")

    print(f"📅 Fetching all transactions from {start_str} to {end_str} (max: {max_transactions})")

    all_transactions = []
    page = 1
    limit_per_page = 100  # API limit

    while len(all_transactions) < max_transactions:
        print(f"   📄 Fetching page {page}...")
        
        # Make API request
        url = "https://api.strackr.com/v4/ad/reports/transactions"
        params = {
            "api_id": api_id,
            "api_key": api_key,
            "time_start": start_str,
            "time_end": end_str,
            "time_type": "sold_at",
            "limit": limit_per_page,
            "page": page,
            "expand": "reason,network_favicon,advertiser_favicon",
        }

        try:
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()
            page_transactions = data.get("data", [])
            
            if not page_transactions:
                print(f"   ✅ No more transactions found on page {page}")
                break
                
            all_transactions.extend(page_transactions)
            print(f"   ✅ Got {len(page_transactions)} transactions (total: {len(all_transactions)})")
            
            # Check if we've reached the limit or if this was the last page
            if len(page_transactions) < limit_per_page or len(all_transactions) >= max_transactions:
                break
                
            page += 1
            
        except Exception as e:
            print(f"   ❌ Error fetching page {page}: {e}")
            break

    # Trim to max_transactions if we exceeded
    if len(all_transactions) > max_transactions:
        all_transactions = all_transactions[:max_transactions]
        
    print(f"✅ Fetched total of {len(all_transactions)} transactions")
    return all_transactions


def transform_and_upload_transactions(raw_transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Transform and upload transactions to Supabase."""
    print(f"\n🔄 Transforming {len(raw_transactions)} transactions...")
    
    # Transform transactions
    normalized_transactions = strackr_transform.transform_strackr_transactions(raw_transactions)
    
    print(f"✅ Successfully transformed {len(normalized_transactions)} transactions")
    print(f"📊 Transformation success rate: {len(normalized_transactions)}/{len(raw_transactions)} ({len(normalized_transactions)/len(raw_transactions)*100:.1f}%)")
    
    if not normalized_transactions:
        return {"success": False, "message": "No transactions to upload"}
    
    # Upload to Supabase
    print(f"\n📤 Uploading {len(normalized_transactions)} transactions to Supabase...")
    
    try:
        client = SupabaseClientLocal()
        result = client.insert_transactions(
            normalized_transactions,
            batch_size=50,  # Smaller batches for reliability
            on_conflict="ignore"  # Ignore duplicates
        )
        
        print(f"📊 Upload Results:")
        print(f"   - Total transactions: {result.get('total_transactions', len(normalized_transactions))}")
        print(f"   - Uploaded transactions: {result.get('inserted_count', 0)}")
        print(f"   - Duplicate/ignored transactions: {len(normalized_transactions) - result.get('inserted_count', 0)}")
        print(f"   - Upload errors: {result.get('error_count', 0)}")
        
        if result.get("errors"):
            print(f"⚠️  Errors encountered:")
            for error in result["errors"][:3]:  # Show first 3 errors
                print(f"     - {error}")
        
        return result
        
    except Exception as e:
        print(f"❌ Upload error: {e}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}


def get_supabase_stats() -> Dict[str, Any]:
    """Get current Supabase statistics."""
    try:
        client = SupabaseClientLocal()
        
        # Get transaction counts
        transactions = client.get_transactions(platform="strackr", limit=1000)
        
        # Get summary data
        summary = client.get_transaction_summary(platform="strackr", days=30)
        
        return {
            "total_transactions": len(transactions),
            "summary_available": summary.get("success", False),
            "summary_days": len(summary.get("data", [])) if summary.get("success") else 0
        }
        
    except Exception as e:
        print(f"❌ Error getting Supabase stats: {e}")
        return {"error": str(e)}


def main():
    """Main function to push all Strackr data to Supabase."""
    print("🚀 Starting Complete Strackr to Supabase Data Push")
    print("=" * 60)
    
    # Get initial stats
    print("📊 Getting initial Supabase statistics...")
    initial_stats = get_supabase_stats()
    if "error" not in initial_stats:
        print(f"   - Current transactions in Supabase: {initial_stats['total_transactions']}")
    
    try:
        # Fetch all transactions
        print("\n🔍 Fetching all recent Strackr transactions...")
        raw_transactions = fetch_all_strackr_transactions(
            days_back=60,  # Get last 60 days
            max_transactions=2000  # Limit to 2000 transactions
        )
        
        if not raw_transactions:
            print("❌ No transactions found!")
            return False
        
        # Transform and upload
        result = transform_and_upload_transactions(raw_transactions)
        
        if not result.get("success", False):
            print("❌ Upload failed!")
            return False
        
        # Get final stats
        print("\n📊 Getting final Supabase statistics...")
        final_stats = get_supabase_stats()
        if "error" not in final_stats:
            print(f"   - Final transactions in Supabase: {final_stats['total_transactions']}")
            if "error" not in initial_stats:
                new_transactions = final_stats['total_transactions'] - initial_stats['total_transactions']
                print(f"   - New transactions added: {new_transactions}")
        
        print("\n🎉 SUCCESS!")
        print("✅ All Strackr data has been pushed to Supabase")
        print("🚀 Pipeline is ready for production use!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Process failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
