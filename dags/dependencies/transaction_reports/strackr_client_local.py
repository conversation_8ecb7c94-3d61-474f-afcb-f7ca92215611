"""
Local development version of Strackr API client.
Uses environment variables for authentication instead of Secret Manager.
"""

import requests
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from .strackr_auth_local import StrackrAuthLocal
from .constants import (
    STRACKR_TRANSACTIONS_ENDPOINT,  # Changed from STRACKR_TRANSACTION_INQUIRIES_ENDPOINT
    DEFAULT_PAGE_SIZE,
    MAX_RETRIES,
    RETRY_DELAY,
    REQUEST_TIMEOUT,
    DEFAULT_TIME_TYPE,
    DEFAULT_CURRENCY,
    STRACKR_EXPAND_FIELDS,
)

logger = logging.getLogger(__name__)


class StrackrClientLocal:
    """
    Local development version of Strackr API client.
    Uses environment variables for authentication.
    """

    def __init__(self):
        """Initialize the Strackr API client with local auth."""
        self.auth = StrackrAuthLocal()
        self.session = requests.Session()
        self.session.timeout = REQUEST_TIMEOUT

        # Validate credentials on initialization
        if not self.auth.validate_credentials():
            raise Exception("Invalid Strackr API credentials")

    def get_transaction_inquiries(
        self,
        start_date: str,
        end_date: str,
        time_type: str = DEFAULT_TIME_TYPE,
        currency: Optional[str] = None,
        networks: Optional[List[str]] = None,
        advertisers: Optional[List[str]] = None,
        statuses: Optional[List[str]] = None,
        limit: int = DEFAULT_PAGE_SIZE,
    ) -> List[Dict[str, Any]]:
        """
        Fetch transaction inquiries from Strackr API.

        Args:
            start_date: Start date in ISO format (YYYY-MM-DDTHH:MM:SSZ)
            end_date: End date in ISO format (YYYY-MM-DDTHH:MM:SSZ)
            time_type: Type of time filter (sold_at, created_at, status_updated_at)
            currency: Currency filter (optional)
            networks: List of network IDs to filter (optional)
            advertisers: List of advertiser IDs to filter (optional)
            statuses: List of status filters (optional)
            limit: Number of results per page (max 100)

        Returns:
            List of transaction inquiry dictionaries
        """
        logger.info(f"Fetching Strackr transactions from {start_date} to {end_date}")

        # Build base parameters
        params = {
            "time_start": start_date,
            "time_end": end_date,
            "time_type": time_type,
            "limit": min(limit, DEFAULT_PAGE_SIZE),
            "page": 1,
            "expand": STRACKR_EXPAND_FIELDS,
        }

        # Add authentication
        params.update(self.auth.get_auth_params())

        # Add optional filters
        if currency:
            params["currency"] = currency
        if networks:
            params["networks"] = networks
        if advertisers:
            params["advertisers"] = advertisers
        if statuses:
            params["statuses"] = statuses

        # Fetch all pages
        all_transactions = []
        page = 1

        while True:
            params["page"] = page
            logger.info(f"Fetching page {page}")

            page_data = self._make_request(
                STRACKR_TRANSACTIONS_ENDPOINT, params  # Updated to use new endpoint
            )

            if not page_data or "data" not in page_data:
                logger.warning(f"No data returned for page {page}")
                break

            transactions = page_data["data"]
            if not transactions:
                logger.info(f"No more transactions on page {page}")
                break

            all_transactions.extend(transactions)
            logger.info(f"Retrieved {len(transactions)} transactions from page {page}")

            # Check if there are more pages
            pagination = page_data.get("pagination")
            if not pagination or not self._has_next_page(pagination, page):
                break

            page += 1

        logger.info(f"Total transactions retrieved: {len(all_transactions)}")
        return all_transactions

    def _make_request(
        self, endpoint: str, params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Make an authenticated request to Strackr API with retries.

        Args:
            endpoint: API endpoint
            params: Request parameters

        Returns:
            Response data as dictionary, None if failed
        """
        url = self.auth.get_authenticated_url(endpoint)

        for attempt in range(MAX_RETRIES + 1):
            try:
                logger.debug(f"Making request to {endpoint} (attempt {attempt + 1})")

                response = self.session.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                logger.debug(
                    f"Request successful, received {len(data.get('data', []))} items"
                )
                return data

            except requests.exceptions.HTTPError as e:
                if response.status_code == 401:
                    logger.error("Authentication failed - check API credentials")
                    raise Exception("Strackr API authentication failed")
                elif response.status_code == 429:
                    logger.warning("Rate limit exceeded, waiting before retry")
                    time.sleep(RETRY_DELAY * 2)  # Longer wait for rate limits
                else:
                    logger.error(f"HTTP error {response.status_code}: {e}")

            except requests.exceptions.RequestException as e:
                logger.error(f"Request failed: {e}")

            except Exception as e:
                logger.error(f"Unexpected error: {e}")

            # Wait before retry (except on last attempt)
            if attempt < MAX_RETRIES:
                wait_time = RETRY_DELAY * (2**attempt)  # Exponential backoff
                logger.info(f"Waiting {wait_time} seconds before retry")
                time.sleep(wait_time)

        logger.error(f"Failed to fetch data after {MAX_RETRIES + 1} attempts")
        return None

    def _has_next_page(self, pagination: Dict[str, Any], current_page: int) -> bool:
        """
        Check if there are more pages to fetch.

        Args:
            pagination: Pagination info from API response
            current_page: Current page number

        Returns:
            True if there are more pages, False otherwise
        """
        if not pagination:
            return False

        # Check various pagination indicators
        if "has_more" in pagination:
            return pagination["has_more"]

        if "total_pages" in pagination:
            return current_page < pagination["total_pages"]

        if "next_page" in pagination:
            return pagination["next_page"] is not None

        # Default: assume no more pages if pagination info is unclear
        return False

    def get_transactions_for_date_range(
        self, days_back: int = 1, time_type: str = DEFAULT_TIME_TYPE
    ) -> List[Dict[str, Any]]:
        """
        Convenience method to get transactions for the last N days.

        Args:
            days_back: Number of days back from today
            time_type: Type of time filter

        Returns:
            List of transaction inquiry dictionaries
        """
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days_back)

        start_str = start_date.strftime("%Y-%m-%dT00:00:00Z")
        end_str = end_date.strftime("%Y-%m-%dT23:59:59Z")

        return self.get_transaction_inquiries(start_str, end_str, time_type)
