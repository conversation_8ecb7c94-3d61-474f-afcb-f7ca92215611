"""
Strackr transaction processing functions for Airflow DAG.
Contains all the functions needed to fetch, process, and normalize Strackr transaction data.
"""

import logging
import time
from datetime import datetime, timezone
from typing import List, Dict, Any
from .strackr_client import StrackrClient
from .strackr_transform import (
    transform_strackr_transactions,
    get_transformation_summary,
)
from .utils import (
    get_date_range_for_yesterday,
    store_transactions_to_gcs,
    validate_transaction_data_quality,
)
from .schema import NormalizedTransaction

logger = logging.getLogger(__name__)


def fetch_strackr_transactions_task(**context) -> Dict[str, Any]:
    """
    Airflow task function to fetch Strackr transaction inquiries.

    Args:
        context: Airflow context dictionary

    Returns:
        Dictionary with task results and metadata
    """
    logger.info("Starting Strackr transaction fetch task")
    start_time = time.time()

    try:
        # Get date range for yesterday
        start_date, end_date = get_date_range_for_yesterday()
        logger.info(f"Fetching transactions for date range: {start_date} to {end_date}")

        # Initialize Strackr client
        client = StrackrClient()

        # Fetch transactions
        raw_transactions = client.get_transactions(
            start_date=start_date, end_date=end_date
        )

        processing_time = time.time() - start_time

        result = {
            "success": True,
            "raw_transaction_count": len(raw_transactions),
            "date_range": {"start_date": start_date, "end_date": end_date},
            "processing_time_seconds": processing_time,
            "raw_transactions": raw_transactions,
        }

        logger.info(f"Successfully fetched {len(raw_transactions)} raw transactions")
        return result

    except Exception as e:
        logger.error(f"Failed to fetch Strackr transactions: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "raw_transaction_count": 0,
            "processing_time_seconds": time.time() - start_time,
        }


def transform_strackr_data_task(**context) -> Dict[str, Any]:
    """
    Airflow task function to transform raw Strackr data to normalized schema.

    Args:
        context: Airflow context dictionary

    Returns:
        Dictionary with transformation results
    """
    logger.info("Starting Strackr data transformation task")
    start_time = time.time()

    try:
        # Get raw transactions from previous task
        fetch_result = context["task_instance"].xcom_pull(
            task_ids="fetch_strackr_transactions"
        )

        if not fetch_result["success"]:
            raise Exception(
                f"Previous task failed: {fetch_result.get('error', 'Unknown error')}"
            )

        raw_transactions = fetch_result["raw_transactions"]
        logger.info(f"Transforming {len(raw_transactions)} raw transactions")

        # Transform to normalized schema
        normalized_transactions = transform_strackr_transactions(raw_transactions)

        # Get transformation summary
        transformation_summary = get_transformation_summary(
            len(raw_transactions), normalized_transactions
        )

        processing_time = time.time() - start_time

        result = {
            "success": True,
            "normalized_transaction_count": len(normalized_transactions),
            "transformation_summary": transformation_summary,
            "processing_time_seconds": processing_time,
            "normalized_transactions": normalized_transactions,
        }

        logger.info(
            f"Successfully transformed {len(normalized_transactions)} transactions"
        )
        return result

    except Exception as e:
        logger.error(f"Failed to transform Strackr data: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "normalized_transaction_count": 0,
            "processing_time_seconds": time.time() - start_time,
        }


def validate_and_store_strackr_data_task(**context) -> Dict[str, Any]:
    """
    Airflow task function to validate and store normalized Strackr data.

    Args:
        context: Airflow context dictionary

    Returns:
        Dictionary with validation and storage results
    """
    logger.info("Starting Strackr data validation and storage task")
    start_time = time.time()

    try:
        # Get normalized transactions from previous task
        transform_result = context["task_instance"].xcom_pull(
            task_ids="transform_strackr_data"
        )

        if not transform_result["success"]:
            raise Exception(
                f"Previous task failed: {transform_result.get('error', 'Unknown error')}"
            )

        normalized_transactions = transform_result["normalized_transactions"]
        logger.info(
            f"Validating {len(normalized_transactions)} normalized transactions"
        )

        # Validate data quality
        validation_results = validate_transaction_data_quality(
            normalized_transactions, platform="strackr"
        )

        # Store to GCS
        date_str = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        gcs_path = store_transactions_to_gcs(
            transactions=normalized_transactions,
            platform="strackr",
            date_str=date_str,
            data_type="processed",
        )

        processing_time = time.time() - start_time

        result = {
            "success": True,
            "validation_results": validation_results,
            "gcs_path": gcs_path,
            "processing_time_seconds": processing_time,
            "final_transaction_count": len(normalized_transactions),
        }

        logger.info(
            f"Successfully validated and stored {len(normalized_transactions)} transactions"
        )
        if gcs_path:
            logger.info(f"Data stored at: {gcs_path}")

        return result

    except Exception as e:
        logger.error(f"Failed to validate and store Strackr data: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "processing_time_seconds": time.time() - start_time,
        }


def get_normalized_strackr_transactions(
    start_date: str, end_date: str
) -> List[NormalizedTransaction]:
    """
    Main function to get normalized Strackr transactions for a date range.
    This is the primary interface for other DAGs to get Strackr data.

    Args:
        start_date: Start date in ISO format (YYYY-MM-DDTHH:MM:SSZ)
        end_date: End date in ISO format (YYYY-MM-DDTHH:MM:SSZ)

    Returns:
        List of normalized transaction objects
    """
    logger.info(
        f"Getting normalized Strackr transactions from {start_date} to {end_date}"
    )

    try:
        # Initialize client and fetch data
        client = StrackrClient()
        raw_transactions = client.get_transactions(start_date, end_date)

        # Transform to normalized schema
        normalized_transactions = transform_strackr_transactions(raw_transactions)

        logger.info(
            f"Retrieved and normalized {len(normalized_transactions)} Strackr transactions"
        )
        return normalized_transactions

    except Exception as e:
        logger.error(f"Failed to get normalized Strackr transactions: {str(e)}")
        raise
