"""
Supabase configuration and constants.
"""

import os
from typing import Dict, Any

# Supabase Configuration
SUPABASE_PROJECT_ID = "fghareehgekdxwljswjg"
SUPABASE_REGION = "us-east-2"
SUPABASE_DEFAULT_URL = f"https://{SUPABASE_PROJECT_ID}.supabase.co"

# Table names
TRANSACTIONS_TABLE = "normalized_transactions"
TRANSACTION_SUMMARY_VIEW = "transaction_summary"
DAILY_PLATFORM_SUMMARY_VIEW = "daily_platform_summary"
MERCHANT_PERFORMANCE_VIEW = "merchant_performance"

# Upload configuration
DEFAULT_BATCH_SIZE = 100
MAX_BATCH_SIZE = 1000
DEFAULT_CONFLICT_RESOLUTION = "ignore"  # Options: "ignore", "update", "error"

# Connection settings
CONNECTION_TIMEOUT = 30
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds

# Environment variable names
ENV_SUPABASE_URL = "SUPABASE_URL"
ENV_SUPABASE_SERVICE_KEY = "SUPABASE_SERVICE_KEY"
ENV_SUPABASE_ANON_KEY = "SUPABASE_ANON_KEY"

def get_supabase_config() -> Dict[str, Any]:
    """
    Get Supabase configuration from environment variables.
    
    Returns:
        Dictionary with Supabase configuration
    """
    return {
        "url": os.getenv(ENV_SUPABASE_URL, SUPABASE_DEFAULT_URL),
        "service_key": os.getenv(ENV_SUPABASE_SERVICE_KEY),
        "anon_key": os.getenv(ENV_SUPABASE_ANON_KEY),
        "project_id": SUPABASE_PROJECT_ID,
        "region": SUPABASE_REGION,
        "batch_size": int(os.getenv("SUPABASE_BATCH_SIZE", DEFAULT_BATCH_SIZE)),
        "max_retries": int(os.getenv("SUPABASE_MAX_RETRIES", MAX_RETRIES)),
        "timeout": int(os.getenv("SUPABASE_TIMEOUT", CONNECTION_TIMEOUT)),
    }

def validate_supabase_config() -> tuple[bool, list[str]]:
    """
    Validate Supabase configuration.
    
    Returns:
        Tuple of (is_valid, error_messages)
    """
    config = get_supabase_config()
    errors = []
    
    if not config["url"]:
        errors.append(f"Missing {ENV_SUPABASE_URL} environment variable")
    
    if not config["service_key"]:
        errors.append(f"Missing {ENV_SUPABASE_SERVICE_KEY} environment variable")
    
    if config["batch_size"] > MAX_BATCH_SIZE:
        errors.append(f"Batch size {config['batch_size']} exceeds maximum {MAX_BATCH_SIZE}")
    
    if config["batch_size"] < 1:
        errors.append(f"Batch size must be at least 1, got {config['batch_size']}")
    
    return len(errors) == 0, errors

# SQL queries for common operations
QUERIES = {
    "get_recent_transactions": """
        SELECT * FROM {table}
        WHERE transaction_date >= NOW() - INTERVAL '{days} days'
        ORDER BY transaction_date DESC
        LIMIT {limit}
    """,
    
    "get_platform_summary": """
        SELECT * FROM {view}
        WHERE platform = '{platform}'
        AND date >= NOW() - INTERVAL '{days} days'
        ORDER BY date DESC
    """,
    
    "get_transaction_count": """
        SELECT COUNT(*) as count
        FROM {table}
        WHERE platform = '{platform}'
        AND transaction_date >= '{start_date}'
        AND transaction_date <= '{end_date}'
    """,
    
    "get_duplicate_transactions": """
        SELECT transaction_id, COUNT(*) as count
        FROM {table}
        GROUP BY transaction_id
        HAVING COUNT(*) > 1
    """,
    
    "delete_old_transactions": """
        DELETE FROM {table}
        WHERE created_at < NOW() - INTERVAL '{days} days'
        AND platform = '{platform}'
    """
}

def get_query(query_name: str, **kwargs) -> str:
    """
    Get a formatted SQL query.
    
    Args:
        query_name: Name of the query from QUERIES dict
        **kwargs: Parameters to format into the query
        
    Returns:
        Formatted SQL query string
    """
    if query_name not in QUERIES:
        raise ValueError(f"Unknown query: {query_name}")
    
    # Set default table/view names
    kwargs.setdefault("table", TRANSACTIONS_TABLE)
    kwargs.setdefault("view", DAILY_PLATFORM_SUMMARY_VIEW)
    
    return QUERIES[query_name].format(**kwargs)
