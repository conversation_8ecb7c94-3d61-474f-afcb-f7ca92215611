"""
Supabase client for storing normalized transaction data.
Handles database operations for both Strackr and ShopMy transaction data.
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import json

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    Client = None

from transaction_reports.schema import NormalizedTransaction

logger = logging.getLogger(__name__)


class SupabaseClient:
    """
    Client for interacting with Supabase database to store normalized transaction data.
    """

    def __init__(self, url: Optional[str] = None, key: Optional[str] = None):
        """
        Initialize Supabase client.
        
        Args:
            url: Supabase project URL (defaults to SUPABASE_URL env var)
            key: Supabase service key (defaults to SUPABASE_SERVICE_KEY env var)
        """
        if not SUPABASE_AVAILABLE:
            raise ImportError("supabase-py package is required. Install with: pip install supabase")
        
        self.url = url or os.getenv("SUPABASE_URL")
        self.key = key or os.getenv("SUPABASE_SERVICE_KEY")
        
        if not self.url or not self.key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables are required")
        
        self.client: Client = create_client(self.url, self.key)
        self.table_name = "normalized_transactions"
        
        logger.info(f"Initialized Supabase client for URL: {self.url}")

    def insert_transactions(
        self, 
        transactions: List[NormalizedTransaction],
        batch_size: int = 100,
        on_conflict: str = "ignore"
    ) -> Dict[str, Any]:
        """
        Insert normalized transactions into Supabase.
        
        Args:
            transactions: List of NormalizedTransaction objects
            batch_size: Number of transactions to insert per batch
            on_conflict: How to handle conflicts ("ignore", "update", or "error")
            
        Returns:
            Dictionary with insertion results and statistics
        """
        if not transactions:
            return {"success": True, "inserted_count": 0, "error_count": 0, "errors": []}
        
        logger.info(f"Inserting {len(transactions)} transactions to Supabase")
        
        total_inserted = 0
        total_errors = 0
        errors = []
        
        # Process in batches
        for i in range(0, len(transactions), batch_size):
            batch = transactions[i:i + batch_size]
            batch_data = [self._transaction_to_dict(tx) for tx in batch]
            
            try:
                if on_conflict == "ignore":
                    result = self.client.table(self.table_name).upsert(
                        batch_data, 
                        on_conflict="transaction_id"
                    ).execute()
                elif on_conflict == "update":
                    result = self.client.table(self.table_name).upsert(
                        batch_data,
                        on_conflict="transaction_id"
                    ).execute()
                else:  # error
                    result = self.client.table(self.table_name).insert(batch_data).execute()
                
                batch_inserted = len(result.data) if result.data else len(batch)
                total_inserted += batch_inserted
                
                logger.info(f"Batch {i//batch_size + 1}: Inserted {batch_inserted} transactions")
                
            except Exception as e:
                error_msg = f"Batch {i//batch_size + 1} failed: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
                total_errors += len(batch)
        
        result = {
            "success": total_errors == 0,
            "inserted_count": total_inserted,
            "error_count": total_errors,
            "errors": errors,
            "total_processed": len(transactions)
        }
        
        logger.info(f"Insertion complete: {total_inserted} inserted, {total_errors} errors")
        return result

    def get_transactions(
        self,
        platform: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        status: Optional[str] = None,
        limit: int = 1000,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Retrieve transactions from Supabase with filtering.
        
        Args:
            platform: Filter by platform ('strackr' or 'shopmy')
            start_date: Filter transactions after this date
            end_date: Filter transactions before this date
            status: Filter by transaction status
            limit: Maximum number of results
            offset: Number of results to skip
            
        Returns:
            List of transaction dictionaries
        """
        query = self.client.table(self.table_name).select("*")
        
        if platform:
            query = query.eq("platform", platform)
        if start_date:
            query = query.gte("transaction_date", start_date.isoformat())
        if end_date:
            query = query.lte("transaction_date", end_date.isoformat())
        if status:
            query = query.eq("status", status)
        
        query = query.order("transaction_date", desc=True).limit(limit).offset(offset)
        
        try:
            result = query.execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Failed to retrieve transactions: {str(e)}")
            raise

    def get_transaction_summary(
        self,
        platform: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get transaction summary statistics.
        
        Args:
            platform: Filter by platform
            days: Number of days to include in summary
            
        Returns:
            Dictionary with summary statistics
        """
        try:
            # Use the daily_platform_summary view
            query = self.client.table("daily_platform_summary").select("*")
            
            if platform:
                query = query.eq("platform", platform)
            
            # Filter by date range
            cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            cutoff_date = cutoff_date.replace(day=cutoff_date.day - days)
            query = query.gte("date", cutoff_date.isoformat())
            
            result = query.execute()
            return {"success": True, "data": result.data or []}
            
        except Exception as e:
            logger.error(f"Failed to get transaction summary: {str(e)}")
            return {"success": False, "error": str(e)}

    def _transaction_to_dict(self, transaction: NormalizedTransaction) -> Dict[str, Any]:
        """
        Convert NormalizedTransaction to dictionary for Supabase insertion.
        
        Args:
            transaction: NormalizedTransaction object
            
        Returns:
            Dictionary representation suitable for Supabase
        """
        data = transaction.to_dict()
        
        # Handle custom_fields JSON serialization
        if data.get("custom_fields"):
            data["custom_fields"] = json.dumps(data["custom_fields"]) if isinstance(data["custom_fields"], dict) else data["custom_fields"]
        
        return data

    def test_connection(self) -> bool:
        """
        Test the Supabase connection.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Try a simple query to test connection
            result = self.client.table(self.table_name).select("id").limit(1).execute()
            logger.info("Supabase connection test successful")
            return True
        except Exception as e:
            logger.error(f"Supabase connection test failed: {str(e)}")
            return False

    def create_table_if_not_exists(self) -> bool:
        """
        Create the normalized_transactions table if it doesn't exist.
        This method executes the schema SQL.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Read the schema file
            schema_path = os.path.join(os.path.dirname(__file__), "..", "..", "supabase", "schema.sql")
            
            if os.path.exists(schema_path):
                with open(schema_path, 'r') as f:
                    schema_sql = f.read()
                
                # Execute the schema
                result = self.client.rpc('exec_sql', {'sql': schema_sql}).execute()
                logger.info("Successfully created/updated database schema")
                return True
            else:
                logger.warning(f"Schema file not found at {schema_path}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to create table schema: {str(e)}")
            return False


class SupabaseClientLocal(SupabaseClient):
    """
    Local version of SupabaseClient that uses environment variables from .env file.
    """
    
    def __init__(self):
        """Initialize with environment variables."""
        # Load from .env if available
        try:
            from dotenv import load_dotenv
            load_dotenv()
        except ImportError:
            pass
        
        super().__init__()
