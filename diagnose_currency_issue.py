#!/usr/bin/env python3
"""
Diagnostic script to check why transactions are still showing EUR instead of USD.
"""

import os
import sys
from datetime import datetime, <PERSON><PERSON><PERSON>

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("dags/dependencies")

import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from supabase_client import SupabaseClientLocal
    print("✅ Supabase client imported successfully")
except ImportError as e:
    print(f"❌ Supabase client import error: {e}")
    sys.exit(1)


def check_api_response():
    """Check what the API is actually returning."""
    print("🔍 Checking Strackr API Response")
    print("=" * 40)
    
    api_id = os.getenv("STRACKR_API_ID")
    api_key = os.getenv("STRACKR_API_KEY")

    if not api_id or not api_key:
        print("❌ Missing API credentials")
        return False

    # Get recent date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)

    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")

    print(f"📅 Fetching from {start_str} to {end_str}")

    # Test 1: Without currency parameter
    print(f"\n🧪 Test 1: API call WITHOUT currency parameter")
    url = "https://api.strackr.com/v4/ad/reports/transactions"
    params_no_currency = {
        "api_id": api_id,
        "api_key": api_key,
        "time_start": start_str,
        "time_end": end_str,
        "time_type": "sold_at",
        "limit": 2,
        "page": 1,
        "expand": "reason,network_favicon,advertiser_favicon",
    }

    try:
        response = requests.get(url, params=params_no_currency, timeout=30)
        response.raise_for_status()
        data = response.json()
        transactions = data.get("data", [])
        
        if transactions:
            tx = transactions[0]
            print(f"   - Currency: {tx.get('currency', 'N/A')}")
            print(f"   - Order Amount: {tx.get('order_amount', 'N/A')}")
            print(f"   - Source Currency: {tx.get('source_currency', 'N/A')}")
        else:
            print("   - No transactions found")
    except Exception as e:
        print(f"   - Error: {e}")

    # Test 2: With USD currency parameter
    print(f"\n🧪 Test 2: API call WITH currency=USD parameter")
    params_with_usd = {
        "api_id": api_id,
        "api_key": api_key,
        "time_start": start_str,
        "time_end": end_str,
        "time_type": "sold_at",
        "limit": 2,
        "page": 1,
        "expand": "reason,network_favicon,advertiser_favicon",
        "currency": "USD",
    }

    try:
        response = requests.get(url, params=params_with_usd, timeout=30)
        response.raise_for_status()
        data = response.json()
        transactions = data.get("data", [])
        
        if transactions:
            tx = transactions[0]
            print(f"   - Currency: {tx.get('currency', 'N/A')}")
            print(f"   - Order Amount: {tx.get('order_amount', 'N/A')}")
            print(f"   - Source Currency: {tx.get('source_currency', 'N/A')}")
        else:
            print("   - No transactions found")
    except Exception as e:
        print(f"   - Error: {e}")

    # Test 3: With EUR currency parameter for comparison
    print(f"\n🧪 Test 3: API call WITH currency=EUR parameter")
    params_with_eur = {
        "api_id": api_id,
        "api_key": api_key,
        "time_start": start_str,
        "time_end": end_str,
        "time_type": "sold_at",
        "limit": 2,
        "page": 1,
        "expand": "reason,network_favicon,advertiser_favicon",
        "currency": "EUR",
    }

    try:
        response = requests.get(url, params=params_with_eur, timeout=30)
        response.raise_for_status()
        data = response.json()
        transactions = data.get("data", [])
        
        if transactions:
            tx = transactions[0]
            print(f"   - Currency: {tx.get('currency', 'N/A')}")
            print(f"   - Order Amount: {tx.get('order_amount', 'N/A')}")
            print(f"   - Source Currency: {tx.get('source_currency', 'N/A')}")
        else:
            print("   - No transactions found")
    except Exception as e:
        print(f"   - Error: {e}")

    return True


def check_supabase_data():
    """Check what's actually stored in Supabase."""
    print(f"\n🔍 Checking Supabase Data")
    print("=" * 40)
    
    try:
        client = SupabaseClientLocal()
        
        # Get a few recent transactions
        result = client.client.table("normalized_transactions").select("*").eq("platform", "strackr").limit(5).execute()
        transactions = result.data
        
        if not transactions:
            print("❌ No transactions found in Supabase")
            return False
        
        print(f"📊 Sample of {len(transactions)} transactions from Supabase:")
        
        currencies_found = set()
        for i, tx in enumerate(transactions):
            currency = tx.get("currency", "Unknown")
            currencies_found.add(currency)
            
            print(f"   Transaction {i+1}:")
            print(f"     - ID: {tx.get('transaction_id', 'N/A')}")
            print(f"     - Currency: {currency}")
            print(f"     - Order Amount: {tx.get('order_amount', 'N/A')}")
            print(f"     - Commission: {tx.get('commission_amount', 'N/A')}")
            print(f"     - Created: {tx.get('created_at', 'N/A')}")
        
        print(f"\n📈 Currency Summary:")
        print(f"   - Currencies in Supabase: {list(currencies_found)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Supabase data: {e}")
        return False


def main():
    """Main diagnostic function."""
    print("🔍 CURRENCY DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Check API responses
    check_api_response()
    
    # Check Supabase data
    check_supabase_data()
    
    print(f"\n" + "=" * 60)
    print("🎯 DIAGNOSTIC COMPLETE")
    print("\n💡 ANALYSIS:")
    print("   - If API shows EUR even with currency=USD, the parameter may not work as expected")
    print("   - If API shows USD but Supabase shows EUR, there's a transformation issue")
    print("   - The Strackr API may not support currency conversion, only filtering")
    print("\n📋 NEXT STEPS:")
    print("   - Check Strackr API documentation for currency parameter behavior")
    print("   - Consider if we need to handle currency conversion in our pipeline")
    print("   - Verify if the currency parameter filters or converts transactions")


if __name__ == "__main__":
    main()
