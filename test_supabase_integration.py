#!/usr/bin/env python3
"""
Test script to validate the complete Strackr to Supabase pipeline.
Tests API fetching, transformation, and Supabase upload.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import required modules
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our modules with proper path handling
sys.path.append("dags/dependencies/transaction_reports")
sys.path.append("dags/dependencies")

# Import modules individually to handle import errors better
try:
    # Import transformation modules
    import strackr_transform
    import schema

    print("✅ Transformation modules imported successfully")
except ImportError as e:
    print(f"❌ Transformation import error: {e}")
    sys.exit(1)

try:
    # Import Supabase modules
    from supabase_client import SupabaseClientLocal

    print("✅ Supabase client imported successfully")
except ImportError as e:
    print(f"❌ Supabase client import error: {e}")
    print("Make sure supabase is installed: pip install supabase")
    sys.exit(1)


# Create a simple uploader class to avoid complex imports
class SimpleTransactionUploader:
    """Simple uploader to avoid import issues."""

    def __init__(self):
        self.client = SupabaseClientLocal()

    def upload_transactions(self, transactions, **kwargs):
        """Upload transactions to Supabase."""
        try:
            return self.client.insert_transactions(transactions, **kwargs)
        except Exception as e:
            return {
                "success": False,
                "total_transactions": len(transactions),
                "uploaded_transactions": 0,
                "errors": [str(e)],
            }


def fetch_sample_transactions(limit: int = 3) -> List[Dict[str, Any]]:
    """Fetch sample transactions from Strackr API."""
    api_id = os.getenv("STRACKR_API_ID")
    api_key = os.getenv("STRACKR_API_KEY")

    if not api_id or not api_key:
        raise ValueError(
            "Missing STRACKR_API_ID or STRACKR_API_KEY environment variables"
        )

    # Get date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)

    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")

    print(f"📅 Fetching {limit} transactions from {start_str} to {end_str}")

    # Make API request
    url = "https://api.strackr.com/v4/ad/reports/transactions"
    params = {
        "api_id": api_id,
        "api_key": api_key,
        "time_start": start_str,
        "time_end": end_str,
        "time_type": "sold_at",
        "limit": limit,
        "page": 1,
        "expand": "reason,network_favicon,advertiser_favicon",
    }

    response = requests.get(url, params=params, timeout=30)
    response.raise_for_status()

    data = response.json()
    return data.get("data", [])


def test_transformation(raw_transactions: List[Dict[str, Any]]) -> List:
    """Test the transformation logic."""
    print(f"\n🔄 Testing transformation of {len(raw_transactions)} transactions...")

    normalized_transactions = strackr_transform.transform_strackr_transactions(
        raw_transactions
    )

    print(f"✅ Successfully transformed {len(normalized_transactions)} transactions")
    print(
        f"📊 Transformation success rate: {len(normalized_transactions)}/{len(raw_transactions)} ({len(normalized_transactions)/len(raw_transactions)*100:.1f}%)"
    )

    if normalized_transactions:
        sample = normalized_transactions[0]
        print(f"\n📄 Sample normalized transaction:")
        print(f"   - Transaction ID: {sample.transaction_id}")
        print(f"   - Platform: {sample.platform}")
        print(f"   - Order Amount: {sample.order_amount} {sample.currency}")
        print(f"   - Commission: {sample.commission_amount} {sample.currency}")
        print(f"   - Status: {sample.status}")
        print(f"   - Merchant: {sample.merchant_name}")
        print(f"   - Network: {sample.network_name}")

    return normalized_transactions


def test_supabase_connection() -> bool:
    """Test Supabase connection."""
    print(f"\n🔗 Testing Supabase connection...")

    try:
        client = SupabaseClientLocal()
        success = client.test_connection()

        if success:
            print("✅ Supabase connection successful")
            return True
        else:
            print("❌ Supabase connection failed")
            return False

    except Exception as e:
        print(f"❌ Supabase connection error: {e}")
        return False


def test_supabase_upload(normalized_transactions: List) -> bool:
    """Test uploading transactions to Supabase."""
    print(
        f"\n📤 Testing Supabase upload of {len(normalized_transactions)} transactions..."
    )

    try:
        uploader = SimpleTransactionUploader()
        result = uploader.upload_transactions(
            normalized_transactions,
            batch_size=10,
            on_conflict="ignore",  # Use ignore for testing to avoid conflicts
        )

        print(f"📊 Upload Results:")
        print(
            f"   - Total transactions: {result.get('total_transactions', len(normalized_transactions))}"
        )
        print(
            f"   - Uploaded transactions: {result.get('inserted_count', result.get('uploaded_transactions', 0))}"
        )
        print(
            f"   - Upload errors: {result.get('error_count', len(result.get('errors', [])))}"
        )

        if result.get("errors"):
            print(f"⚠️  Errors encountered:")
            for error in result["errors"][:3]:  # Show first 3 errors
                print(f"     - {error}")

        if result.get("success", True) and result.get("inserted_count", 0) > 0:
            print("✅ Supabase upload successful")
            return True
        else:
            print("❌ Supabase upload failed")
            return False

    except Exception as e:
        print(f"❌ Supabase upload error: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_supabase_query() -> bool:
    """Test querying data from Supabase."""
    print(f"\n🔍 Testing Supabase data retrieval...")

    try:
        client = SupabaseClientLocal()

        # Get recent transactions
        transactions = client.get_transactions(platform="strackr", limit=5)

        print(f"📄 Retrieved {len(transactions)} transactions from Supabase")

        if transactions:
            sample = transactions[0]
            print(f"   - Sample transaction ID: {sample.get('transaction_id', 'N/A')}")
            print(f"   - Platform: {sample.get('platform', 'N/A')}")
            print(f"   - Status: {sample.get('status', 'N/A')}")
            print(f"   - Created: {sample.get('created_at', 'N/A')}")

        # Get summary
        summary = client.get_transaction_summary(platform="strackr", days=7)
        if summary.get("success"):
            print(
                f"📊 Summary data available: {len(summary.get('data', []))} daily summaries"
            )

        print("✅ Supabase query successful")
        return True

    except Exception as e:
        print(f"❌ Supabase query error: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 Starting Complete Supabase Integration Test")
    print("=" * 60)

    success_count = 0
    total_tests = 5

    try:
        # Test 1: Fetch transactions from API
        print("🧪 Test 1: Fetch Strackr Transactions")
        raw_transactions = fetch_sample_transactions(limit=3)
        print(f"✅ Fetched {len(raw_transactions)} raw transactions")
        success_count += 1

        # Test 2: Transform transactions
        print("\n🧪 Test 2: Transform Transactions")
        normalized_transactions = test_transformation(raw_transactions)
        if normalized_transactions:
            print("✅ Transformation successful")
            success_count += 1
        else:
            print("❌ Transformation failed")

        # Test 3: Test Supabase connection
        print("\n🧪 Test 3: Supabase Connection")
        if test_supabase_connection():
            success_count += 1

        # Test 4: Upload to Supabase
        if normalized_transactions:
            print("\n🧪 Test 4: Upload to Supabase")
            if test_supabase_upload(normalized_transactions):
                success_count += 1
        else:
            print("\n⏭️  Skipping Supabase upload test (no normalized transactions)")

        # Test 5: Query from Supabase
        print("\n🧪 Test 5: Query from Supabase")
        if test_supabase_query():
            success_count += 1

    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback

        traceback.print_exc()

    # Final results
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {success_count}/{total_tests} tests passed")

    if success_count == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Complete pipeline is working correctly")
        print("🚀 Ready for production use!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        print("🔧 Please check the failed components")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
