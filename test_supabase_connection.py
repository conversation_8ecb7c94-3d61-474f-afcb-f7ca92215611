#!/usr/bin/env python3
"""
Quick test script to verify Supabase connection and credentials.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("dags/dependencies")

def test_supabase_connection():
    """Test basic Supabase connection."""
    print("🔗 Testing Supabase Connection...")
    print("=" * 50)
    
    # Check environment variables
    url = os.getenv("SUPABASE_URL")
    service_key = os.getenv("SUPABASE_SERVICE_KEY")
    anon_key = os.getenv("SUPABASE_ANON_KEY")
    
    print(f"📋 Environment Variables:")
    print(f"   - SUPABASE_URL: {'✅ Set' if url else '❌ Missing'}")
    print(f"   - SUPABASE_SERVICE_KEY: {'✅ Set' if service_key else '❌ Missing'}")
    print(f"   - SUPABASE_ANON_KEY: {'✅ Set' if anon_key else '❌ Missing'}")
    
    if not url or not service_key:
        print("\n❌ Missing required environment variables!")
        print("Please check your .env file")
        return False
    
    try:
        from supabase_client import SupabaseClientLocal
        
        print(f"\n🔌 Connecting to Supabase...")
        client = SupabaseClientLocal()
        
        print(f"🧪 Testing connection...")
        success = client.test_connection()
        
        if success:
            print("✅ Supabase connection successful!")
            
            # Try to query the table structure
            print(f"\n📊 Testing table access...")
            try:
                result = client.client.table("normalized_transactions").select("id").limit(1).execute()
                print("✅ Table access successful!")
                print(f"   - Table exists and is accessible")
                return True
            except Exception as e:
                print(f"⚠️  Table access issue: {e}")
                print("   - Connection works but table might not exist or have permission issues")
                return False
        else:
            print("❌ Supabase connection failed!")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please install supabase: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    success = test_supabase_connection()
    if success:
        print(f"\n🎉 Ready to test the full pipeline!")
        print(f"Run: python test_supabase_integration.py")
    else:
        print(f"\n🔧 Please fix the connection issues first")
    
    sys.exit(0 if success else 1)
