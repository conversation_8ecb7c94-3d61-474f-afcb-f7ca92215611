#!/usr/bin/env python3
"""
Final verification test to ensure the complete Strackr to Supabase pipeline is working.
This script verifies data integrity and pipeline readiness.
"""

import os
import sys
from datetime import datetime, timedelta

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("dags/dependencies")

from dotenv import load_dotenv
load_dotenv()

try:
    from supabase_client import SupabaseClientLocal
    print("✅ Supabase client imported successfully")
except ImportError as e:
    print(f"❌ Supabase client import error: {e}")
    sys.exit(1)


def verify_supabase_data():
    """Verify the data in Supabase is correct and complete."""
    print("🔍 Verifying Supabase Data Integrity")
    print("=" * 40)
    
    try:
        client = SupabaseClientLocal()
        
        # Test 1: Connection
        print("\n🧪 Test 1: Connection Test")
        if client.test_connection():
            print("✅ Supabase connection successful")
        else:
            print("❌ Supabase connection failed")
            return False
        
        # Test 2: Data Count
        print("\n🧪 Test 2: Data Count Verification")
        transactions = client.get_transactions(platform="strackr", limit=10000)
        total_count = len(transactions)
        print(f"✅ Found {total_count} total transactions in Supabase")
        
        if total_count == 0:
            print("❌ No transactions found in Supabase")
            return False
        
        # Test 3: Data Quality
        print("\n🧪 Test 3: Data Quality Checks")
        
        # Check for required fields
        sample_transaction = transactions[0] if transactions else None
        if sample_transaction:
            required_fields = ['transaction_id', 'platform', 'status', 'merchant_name', 'created_at']
            missing_fields = [field for field in required_fields if not sample_transaction.get(field)]
            
            if missing_fields:
                print(f"❌ Missing required fields: {missing_fields}")
                return False
            else:
                print("✅ All required fields present")
        
        # Test 4: Status Distribution
        print("\n🧪 Test 4: Status Distribution")
        status_counts = {}
        for tx in transactions:
            status = tx.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("📊 Transaction Status Distribution:")
        for status, count in status_counts.items():
            percentage = (count / total_count) * 100
            print(f"   - {status}: {count} ({percentage:.1f}%)")
        
        # Test 5: Recent Data
        print("\n🧪 Test 5: Recent Data Check")
        recent_transactions = [tx for tx in transactions if tx.get('created_at')]
        if recent_transactions:
            latest_created = max(tx['created_at'] for tx in recent_transactions)
            print(f"✅ Latest transaction created: {latest_created}")
        else:
            print("⚠️  No transactions with creation timestamps found")
        
        # Test 6: Platform Consistency
        print("\n🧪 Test 6: Platform Consistency")
        platforms = set(tx.get('platform') for tx in transactions)
        if platforms == {'strackr'}:
            print("✅ All transactions are from Strackr platform")
        else:
            print(f"⚠️  Found unexpected platforms: {platforms}")
        
        print(f"\n✅ Data verification completed successfully!")
        print(f"📊 Summary: {total_count} transactions verified")
        
        return True
        
    except Exception as e:
        print(f"❌ Data verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_pipeline_readiness():
    """Verify that the pipeline is ready for production use."""
    print("\n🚀 Verifying Pipeline Readiness")
    print("=" * 40)
    
    try:
        # Check environment variables
        print("\n🧪 Environment Configuration")
        required_env_vars = [
            'STRACKR_API_ID',
            'STRACKR_API_KEY', 
            'SUPABASE_URL',
            'SUPABASE_SERVICE_KEY'
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing environment variables: {missing_vars}")
            return False
        else:
            print("✅ All required environment variables are set")
        
        # Check file structure
        print("\n🧪 File Structure Check")
        required_files = [
            'dags/strackr_csv.py',
            'dags/dependencies/transaction_reports/strackr_csv.py',
            'dags/dependencies/transaction_reports/strackr_transform.py',
            'dags/dependencies/transaction_reports/supabase_upload.py',
            'dags/dependencies/supabase_client.py',
            'supabase/schema.sql'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ Missing required files: {missing_files}")
            return False
        else:
            print("✅ All required files are present")
        
        # Test Supabase connection
        print("\n🧪 Supabase Connection Test")
        client = SupabaseClientLocal()
        if client.test_connection():
            print("✅ Supabase connection working")
        else:
            print("❌ Supabase connection failed")
            return False
        
        print("\n✅ Pipeline readiness verification completed!")
        print("🚀 Pipeline is ready for production deployment!")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline readiness check failed: {e}")
        return False


def generate_summary_report():
    """Generate a summary report of the current state."""
    print("\n📋 Generating Summary Report")
    print("=" * 40)
    
    try:
        client = SupabaseClientLocal()
        transactions = client.get_transactions(platform="strackr", limit=10000)
        
        # Basic stats
        total_transactions = len(transactions)
        
        # Status breakdown
        status_counts = {}
        total_order_amount = 0
        total_commission = 0
        
        for tx in transactions:
            status = tx.get('status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
            
            # Sum amounts (convert to float, handle None/empty values)
            try:
                order_amount = float(tx.get('order_amount', 0) or 0)
                commission_amount = float(tx.get('commission_amount', 0) or 0)
                total_order_amount += order_amount
                total_commission += commission_amount
            except (ValueError, TypeError):
                pass
        
        print(f"\n📊 STRACKR TO SUPABASE PIPELINE SUMMARY")
        print(f"=" * 50)
        print(f"📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"")
        print(f"📈 TRANSACTION STATISTICS:")
        print(f"   • Total Transactions: {total_transactions:,}")
        print(f"   • Total Order Amount: €{total_order_amount:,.2f}")
        print(f"   • Total Commission: €{total_commission:,.2f}")
        print(f"   • Average Order Value: €{total_order_amount/max(total_transactions,1):,.2f}")
        print(f"")
        print(f"📊 STATUS BREAKDOWN:")
        for status, count in sorted(status_counts.items()):
            percentage = (count / max(total_transactions, 1)) * 100
            print(f"   • {status.title()}: {count:,} ({percentage:.1f}%)")
        print(f"")
        print(f"✅ PIPELINE STATUS: OPERATIONAL")
        print(f"🚀 READY FOR: Production Deployment")
        print(f"📋 NEXT STEPS: Deploy to Airflow and schedule daily runs")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to generate summary report: {e}")
        return False


def main():
    """Main verification function."""
    print("🎯 FINAL PIPELINE VERIFICATION")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Data Verification
    if verify_supabase_data():
        success_count += 1
    
    # Test 2: Pipeline Readiness
    if verify_pipeline_readiness():
        success_count += 1
    
    # Test 3: Summary Report
    if generate_summary_report():
        success_count += 1
    
    # Final results
    print(f"\n" + "=" * 60)
    print(f"🎯 VERIFICATION RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 COMPLETE SUCCESS!")
        print("✅ Strackr to Supabase pipeline is fully operational")
        print("🚀 Ready for production deployment!")
        print("📋 All data has been successfully pushed to Supabase")
        return True
    else:
        print("❌ VERIFICATION INCOMPLETE!")
        print("🔧 Please address the failed components")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
