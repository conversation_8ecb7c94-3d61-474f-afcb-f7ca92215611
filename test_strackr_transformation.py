#!/usr/bin/env python3
"""
Test script to validate Strackr transformation logic with the new transactions endpoint.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import transformation module directly (avoid client dependencies)
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import transformation function directly
sys.path.append("dags/dependencies/transaction_reports")

# Import required modules
import schema
import strackr_transform


def fetch_sample_transactions():
    """Fetch sample transactions directly from API."""
    api_id = os.getenv("STRACKR_API_ID")
    api_key = os.getenv("STRACKR_API_KEY")

    if not api_id or not api_key:
        raise ValueError(
            "Missing STRACKR_API_ID or STRACKR_API_KEY environment variables"
        )

    # Get date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)

    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")

    print(f"📅 Fetching transactions from {start_str} to {end_str}")

    # Make API request
    url = "https://api.strackr.com/v4/ad/reports/transactions"
    params = {
        "api_id": api_id,
        "api_key": api_key,
        "time_start": start_str,
        "time_end": end_str,
        "time_type": "sold_at",
        "limit": 5,  # Small sample for testing
        "page": 1,
        "expand": "reason,network_favicon,advertiser_favicon",
    }

    response = requests.get(url, params=params, timeout=30)
    response.raise_for_status()

    data = response.json()
    return data.get("data", [])


def test_transformation():
    """Test the transformation logic with real API data."""
    print("🧪 Testing Strackr Transformation Logic")
    print("=" * 60)

    try:
        # Fetch raw transactions
        raw_transactions = fetch_sample_transactions()

        print(f"📄 Retrieved {len(raw_transactions)} raw transactions")

        if not raw_transactions:
            print("⚠️  No transactions to test transformation")
            return False

        # Test transformation
        print("\n🔄 Testing transformation...")
        normalized_transactions = strackr_transform.transform_strackr_transactions(
            raw_transactions
        )

        print(
            f"✅ Successfully transformed {len(normalized_transactions)} transactions"
        )
        print(
            f"📊 Transformation success rate: {len(normalized_transactions)}/{len(raw_transactions)} ({len(normalized_transactions)/len(raw_transactions)*100:.1f}%)"
        )

        if normalized_transactions:
            sample = normalized_transactions[0]
            print(f"\n📄 Sample normalized transaction:")
            print(f"   - Transaction ID: {sample.transaction_id}")
            print(f"   - Order ID: {sample.order_id}")
            print(f"   - Platform: {sample.platform}")
            print(f"   - Network: {sample.network_name}")
            print(f"   - Merchant: {sample.merchant_name}")
            print(f"   - Currency: {sample.currency}")
            print(f"   - Order Amount: {sample.order_amount}")
            print(f"   - Commission: {sample.commission_amount}")
            print(f"   - Status: {sample.status}")
            print(f"   - Transaction Date: {sample.transaction_date}")
            print(f"   - Created Date: {sample.created_date}")

            # Test serialization
            print(f"\n🔧 Testing serialization...")
            sample_dict = sample.to_dict()
            print(f"✅ Serialization successful - {len(sample_dict)} fields")

        return True

    except Exception as e:
        print(f"❌ Transformation test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 Starting Strackr Transformation Test")
    print("=" * 60)

    success = test_transformation()

    if success:
        print("\n" + "=" * 60)
        print("🎉 ALL TRANSFORMATION TESTS PASSED!")
        print("✅ Transformation logic is working correctly")
        print("✅ Field mappings are correct")
        print("✅ Data serialization works")
        print("🚀 Ready for production use!")
    else:
        print("\n" + "=" * 60)
        print("❌ TRANSFORMATION TESTS FAILED!")
        print("🔧 Please check the transformation logic")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
