#!/usr/bin/env python3
"""
Script to wipe all Strackr transaction data from Supabase database.
This prepares the database for re-importing data with USD currency.
"""

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.append("dags/dependencies")

from dotenv import load_dotenv
load_dotenv()

try:
    from supabase_client import SupabaseClientLocal
    print("✅ Supabase client imported successfully")
except ImportError as e:
    print(f"❌ Supabase client import error: {e}")
    sys.exit(1)


def get_current_data_stats():
    """Get current data statistics before deletion."""
    try:
        client = SupabaseClientLocal()
        
        # Get all transactions
        result = client.client.table("normalized_transactions").select("*").eq("platform", "strackr").execute()
        transactions = result.data
        
        if not transactions:
            return {"total_count": 0, "currencies": [], "status_breakdown": {}}
        
        # Analyze current data
        currencies = set()
        status_counts = {}
        
        for tx in transactions:
            currency = tx.get("currency", "Unknown")
            status = tx.get("status", "Unknown")
            
            currencies.add(currency)
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_count": len(transactions),
            "currencies": list(currencies),
            "status_breakdown": status_counts
        }
        
    except Exception as e:
        print(f"❌ Error getting current data stats: {e}")
        return None


def wipe_strackr_data():
    """Delete all Strackr transaction data from Supabase."""
    print("🗑️  Wiping Strackr Data from Supabase")
    print("=" * 40)
    
    try:
        client = SupabaseClientLocal()
        
        # Get current stats
        print("📊 Getting current data statistics...")
        current_stats = get_current_data_stats()
        
        if current_stats is None:
            print("❌ Failed to get current data statistics")
            return False
        
        if current_stats["total_count"] == 0:
            print("✅ No Strackr data found in database - already clean!")
            return True
        
        print(f"📈 Current Data Summary:")
        print(f"   - Total transactions: {current_stats['total_count']:,}")
        print(f"   - Currencies: {current_stats['currencies']}")
        print(f"   - Status breakdown: {current_stats['status_breakdown']}")
        
        # Confirm deletion
        print(f"\n⚠️  WARNING: This will delete ALL {current_stats['total_count']:,} Strackr transactions!")
        print("   This action cannot be undone.")
        
        # For safety, require explicit confirmation
        confirmation = input("\nType 'DELETE ALL STRACKR DATA' to confirm: ")
        
        if confirmation != "DELETE ALL STRACKR DATA":
            print("❌ Deletion cancelled - confirmation text did not match")
            return False
        
        print(f"\n🗑️  Deleting all Strackr transaction data...")
        
        # Delete all Strackr transactions
        result = client.client.table("normalized_transactions").delete().eq("platform", "strackr").execute()
        
        print(f"✅ Successfully deleted Strackr transaction data")
        
        # Verify deletion
        print(f"\n🔍 Verifying deletion...")
        verification_result = client.client.table("normalized_transactions").select("id").eq("platform", "strackr").execute()
        remaining_count = len(verification_result.data)
        
        if remaining_count == 0:
            print("✅ Verification successful - no Strackr data remaining")
            return True
        else:
            print(f"⚠️  Warning: {remaining_count} Strackr transactions still remain")
            return False
        
    except Exception as e:
        print(f"❌ Failed to wipe Strackr data: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function."""
    print("🚀 Supabase Data Wipe Utility")
    print("=" * 50)
    
    success = wipe_strackr_data()
    
    if success:
        print(f"\n🎉 SUCCESS!")
        print("✅ All Strackr data has been wiped from Supabase")
        print("🚀 Database is now ready for USD data import")
        return True
    else:
        print(f"\n❌ FAILED!")
        print("🔧 Please check the errors above")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
